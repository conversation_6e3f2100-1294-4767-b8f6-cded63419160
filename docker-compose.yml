version: '3.8'

services:
  # Vehicle Tracking Service
  vehicle-tracking-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
    depends_on:
      - localstack
    networks:
      - vehicle-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/vehicles/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # LocalStack for local AWS services testing
  localstack:
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"  # LocalStack main port
      - "4510-4559:4510-4559"  # External service ports
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./localstack-data:/tmp/localstack"
    networks:
      - vehicle-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx for load balancing (optional, for production-like setup)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - vehicle-tracking-service
    networks:
      - vehicle-network
    restart: unless-stopped

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - vehicle-network
    restart: unless-stopped

networks:
  vehicle-network:
    driver: bridge

volumes:
  localstack-data:
