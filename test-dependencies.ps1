Write-Host "🔍 Testing Maven Dependencies..." -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Check if Maven is available
try {
    $mvnVersion = mvn --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Maven found" -ForegroundColor Green
    } else {
        throw "Maven not found"
    }
} catch {
    Write-Host "❌ Maven is not installed. Please install Maven to test dependencies." -ForegroundColor Red
    exit 1
}

# Validate POM structure
Write-Host "📋 Validating POM structure..." -ForegroundColor Yellow
mvn validate

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ POM structure is valid" -ForegroundColor Green
} else {
    Write-Host "❌ POM structure validation failed" -ForegroundColor Red
    exit 1
}

# Test dependency resolution (without downloading - just validate)
Write-Host "📦 Testing dependency resolution..." -ForegroundColor Yellow
mvn dependency:resolve-sources -DexcludeTransitive=true

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ All dependencies resolved successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Dependency resolution failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 All dependency tests passed!" -ForegroundColor Green
Write-Host "The pom.xml file is now fixed and ready for building." -ForegroundColor Green
