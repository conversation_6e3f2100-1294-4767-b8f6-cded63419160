#!/bin/bash

# Comprehensive test runner for Vehicle Tracking Service
set -e

echo "🧪 Vehicle Tracking Service - Test Suite Runner"
echo "=============================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is required but not installed"
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose is required but not installed"
    exit 1
fi

if ! command_exists mvn; then
    echo "❌ Maven is required but not installed"
    exit 1
fi

echo "✅ All prerequisites satisfied"
echo ""

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "✅ $service_name is ready"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within timeout"
    return 1
}

# Clean up function
cleanup() {
    echo "🧹 Cleaning up..."
    docker-compose down -v --remove-orphans
    docker system prune -f
}

# Set trap for cleanup on exit
trap cleanup EXIT

echo "1️⃣ Running Unit Tests"
echo "===================="
mvn clean test -Dtest='!*IntegrationTest'
echo "✅ Unit tests completed"
echo ""

echo "2️⃣ Starting Test Environment"
echo "============================"
docker-compose down -v --remove-orphans
docker-compose up -d localstack

# Wait for LocalStack
wait_for_service "http://localhost:4566/_localstack/health" "LocalStack"

echo ""

echo "3️⃣ Running Integration Tests"
echo "============================"
mvn test -Dtest=VehicleTrackingIntegrationTest
echo "✅ Integration tests completed"
echo ""

echo "4️⃣ Building and Starting Application"
echo "===================================="
docker-compose up -d --build vehicle-tracking-service

# Wait for application
wait_for_service "http://localhost:8080/api/v1/vehicles/health" "Vehicle Tracking Service"

echo ""

echo "5️⃣ Running API Tests"
echo "==================="
chmod +x test-api.sh
./test-api.sh
echo "✅ API tests completed"
echo ""

echo "6️⃣ Running Load Tests (Optional)"
echo "==============================="
if command_exists ab; then
    echo "Running basic load test with Apache Bench..."
    
    # Create a test vehicle first
    curl -s -X POST http://localhost:8080/api/v1/vehicles/LOAD_TEST_VEHICLE > /dev/null
    
    # Run load test
    ab -n 100 -c 10 http://localhost:8080/api/v1/vehicles/LOAD_TEST_VEHICLE
    
    echo "✅ Load test completed"
else
    echo "⚠️  Apache Bench (ab) not found, skipping load tests"
fi
echo ""

echo "7️⃣ Checking Service Health"
echo "=========================="

# Check main service health
echo "Main service health:"
curl -s http://localhost:8080/api/v1/vehicles/health

echo ""
echo "Webhook service health:"
curl -s http://localhost:8080/api/v1/webhooks/health | jq '.'

echo ""
echo "Spring Boot actuator health:"
curl -s http://localhost:8080/api/v1/actuator/health | jq '.'

echo ""

echo "8️⃣ Generating Test Report"
echo "========================="

# Create test report directory
mkdir -p test-reports

# Generate coverage report
mvn jacoco:report

# Copy reports
cp -r target/site/jacoco test-reports/coverage
cp target/surefire-reports/*.xml test-reports/ 2>/dev/null || true

echo "✅ Test reports generated in test-reports/ directory"
echo ""

echo "🎉 All Tests Completed Successfully!"
echo "===================================="
echo "Test Summary:"
echo "✅ Unit tests passed"
echo "✅ Integration tests passed"
echo "✅ API tests passed"
echo "✅ Service health checks passed"
echo "✅ Application is running correctly"
echo ""
echo "Services are still running. Use 'docker-compose down' to stop them."
echo "View logs with: docker-compose logs -f"
echo "Access application at: http://localhost:8080/api/v1"
