#!/bin/bash

echo "🔍 Testing Maven Dependencies..."
echo "================================"

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed. Please install <PERSON><PERSON> to test dependencies."
    exit 1
fi

echo "✅ <PERSON><PERSON> found"

# Validate POM structure
echo "📋 Validating POM structure..."
mvn validate

if [ $? -eq 0 ]; then
    echo "✅ POM structure is valid"
else
    echo "❌ POM structure validation failed"
    exit 1
fi

# Test dependency resolution
echo "📦 Testing dependency resolution..."
mvn dependency:resolve

if [ $? -eq 0 ]; then
    echo "✅ All dependencies resolved successfully"
else
    echo "❌ Dependency resolution failed"
    exit 1
fi

echo ""
echo "🎉 All dependency tests passed!"
echo "The pom.xml file is now fixed and ready for building."
