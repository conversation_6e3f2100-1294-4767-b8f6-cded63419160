#!/bin/bash

# Vehicle Tracking Service API Test Script
# This script tests all the main API endpoints

set -e

BASE_URL="http://localhost:8080/api/v1"
VEHICLE_NAME="TEST_VEHICLE_$(date +%s)"

echo "🚀 Starting Vehicle Tracking Service API Tests"
echo "Base URL: $BASE_URL"
echo "Test Vehicle: $VEHICLE_NAME"
echo "=================================="

# Function to make HTTP requests with error handling
make_request() {
    local method=$1
    local url=$2
    local data=$3
    local expected_status=$4
    
    echo "📡 $method $url"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    # Split response and status code
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    echo "Status: $status"
    echo "Response: $body"
    
    if [ "$status" != "$expected_status" ]; then
        echo "❌ Expected status $expected_status, got $status"
        exit 1
    fi
    
    echo "✅ Success"
    echo ""
}

# Test 1: Health Check
echo "1. Testing Health Check"
make_request "GET" "$BASE_URL/vehicles/health" "" "200"

# Test 2: Create Vehicle
echo "2. Creating Vehicle"
make_request "POST" "$BASE_URL/vehicles/$VEHICLE_NAME" "" "201"

# Test 3: Check Vehicle Exists
echo "3. Checking Vehicle Exists"
make_request "GET" "$BASE_URL/vehicles/$VEHICLE_NAME/exists" "" "200"

# Test 4: Get Vehicle Data
echo "4. Getting Vehicle Data"
make_request "GET" "$BASE_URL/vehicles/$VEHICLE_NAME" "" "200"

# Test 5: Update Location via Webhook
echo "5. Updating Location via Webhook"
location_data='{
    "vehicle_name": "'$VEHICLE_NAME'",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "speed": 25.5,
    "heading": 180.0,
    "accuracy": 5.0
}'
make_request "POST" "$BASE_URL/webhooks/location" "$location_data" "200"

# Test 6: Update Video URL via Webhook
echo "6. Updating Video URL via Webhook"
video_data='{
    "vehicle_name": "'$VEHICLE_NAME'",
    "video_url": "http://example.com/video/stream",
    "quality": "HD",
    "provider": "test-provider",
    "expiry_hours": 24
}'
make_request "POST" "$BASE_URL/webhooks/video-url" "$video_data" "200"

# Test 7: Get Updated Vehicle Data
echo "7. Getting Updated Vehicle Data"
make_request "GET" "$BASE_URL/vehicles/$VEHICLE_NAME" "" "200"

# Test 8: Generic Webhook - Location
echo "8. Testing Generic Webhook - Location"
generic_location='{
    "type": "location",
    "vehicle_name": "'$VEHICLE_NAME'",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "speed": 30.0,
    "heading": 90.0
}'
make_request "POST" "$BASE_URL/webhooks/generic" "$generic_location" "200"

# Test 9: Generic Webhook - Video
echo "9. Testing Generic Webhook - Video"
generic_video='{
    "type": "video",
    "vehicle_name": "'$VEHICLE_NAME'",
    "video_url": "http://example.com/video/stream2",
    "quality": "FHD",
    "provider": "generic-provider"
}'
make_request "POST" "$BASE_URL/webhooks/generic" "$generic_video" "200"

# Test 10: Webhook Health Check
echo "10. Testing Webhook Health Check"
make_request "GET" "$BASE_URL/webhooks/health" "" "200"

# Test 11: Try to Get Non-existent Vehicle
echo "11. Testing Non-existent Vehicle"
make_request "GET" "$BASE_URL/vehicles/NONEXISTENT_VEHICLE" "" "404"

# Test 12: Delete Vehicle
echo "12. Deleting Vehicle"
make_request "DELETE" "$BASE_URL/vehicles/$VEHICLE_NAME" "" "204"

# Test 13: Verify Vehicle is Deleted
echo "13. Verifying Vehicle is Deleted"
make_request "GET" "$BASE_URL/vehicles/$VEHICLE_NAME/exists" "" "404"

echo "🎉 All tests passed successfully!"
echo "=================================="
echo "Test Summary:"
echo "- Vehicle created and managed successfully"
echo "- Location updates working"
echo "- Video URL updates working"
echo "- Webhook endpoints functioning"
echo "- Error handling working correctly"
echo "- Cleanup completed"
