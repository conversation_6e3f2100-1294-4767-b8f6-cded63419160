# Vehicle Tracking Service

A fault-tolerant, cost-effective Spring Boot service for vehicle tracking with AWS S3 storage, designed for scalability and reliability.

## Features

- **Vehicle Management**: Create and manage vehicle objects in S3
- **Location Tracking**: Store and retrieve 24-hour location history
- **Live Video URLs**: Manage time-expiring video stream URLs
- **Webhook Integration**: Receive location and video updates from external systems
- **Fault Tolerance**: Circuit breakers, retries, and graceful error handling
- **Docker Support**: Complete containerization for local development and testing
- **Monitoring**: Health checks, metrics, and Prometheus integration

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Vehicle       │    │   AWS S3        │
│   Systems       │───▶│   Tracking      │───▶│   Storage       │
│   (Webhooks)    │    │   Service       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Monitoring    │
                       │   & Metrics     │
                       └─────────────────┘
```

## API Endpoints

### Vehicle Management
- `POST /api/v1/vehicles/{vehicleName}` - Create or get vehicle
- `GET /api/v1/vehicles/{vehicleName}` - Get vehicle data
- `GET /api/v1/vehicles/{vehicleName}/exists` - Check if vehicle exists
- `DELETE /api/v1/vehicles/{vehicleName}` - Delete vehicle

### Webhooks
- `POST /api/v1/webhooks/location` - Update vehicle location
- `POST /api/v1/webhooks/video-url` - Update live video URL
- `POST /api/v1/webhooks/generic` - Generic webhook handler

### Health & Monitoring
- `GET /api/v1/vehicles/health` - Service health check
- `GET /api/v1/webhooks/health` - Webhook health check
- `GET /api/v1/actuator/health` - Spring Boot health endpoint
- `GET /api/v1/actuator/metrics` - Application metrics

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Java 17+ (for local development)
- Maven 3.6+ (for local development)

### Running with Docker

1. **Clone and start the services:**
   ```bash
   git clone <repository-url>
   cd vlstransmeta
   docker-compose up -d
   ```

2. **Verify services are running:**
   ```bash
   curl http://localhost:8080/api/v1/vehicles/health
   ```

3. **Create a vehicle:**
   ```bash
   curl -X POST http://localhost:8080/api/v1/vehicles/TRUCK_001
   ```

4. **Update vehicle location:**
   ```bash
   curl -X POST http://localhost:8080/api/v1/webhooks/location \
     -H "Content-Type: application/json" \
     -d '{
       "vehicle_name": "TRUCK_001",
       "latitude": 40.7128,
       "longitude": -74.0060,
       "speed": 25.5,
       "heading": 180.0
     }'
   ```

5. **Update video URL:**
   ```bash
   curl -X POST http://localhost:8080/api/v1/webhooks/video-url \
     -H "Content-Type: application/json" \
     -d '{
       "vehicle_name": "TRUCK_001",
       "video_url": "http://example.com/video/stream",
       "quality": "HD",
       "expiry_hours": 24
     }'
   ```

6. **Get vehicle data:**
   ```bash
   curl http://localhost:8080/api/v1/vehicles/TRUCK_001
   ```

### Running Tests

```bash
# Run all tests
mvn test

# Run integration tests with TestContainers
mvn test -Dtest=VehicleTrackingIntegrationTest

# Run with coverage
mvn test jacoco:report
```

### Local Development

1. **Start LocalStack for S3:**
   ```bash
   docker-compose up localstack -d
   ```

2. **Set environment variables:**
   ```bash
   export AWS_ACCESS_KEY_ID=test
   export AWS_SECRET_ACCESS_KEY=test
   export AWS_REGION=us-east-1
   ```

3. **Run the application:**
   ```bash
   mvn spring-boot:run -Dspring-boot.run.profiles=docker
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `AWS_ACCESS_KEY_ID` | AWS access key | - |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | - |
| `AWS_REGION` | AWS region | us-east-1 |
| `AWS_S3_BUCKET_NAME` | S3 bucket name | vehicle-tracking-data |
| `AWS_S3_ENDPOINT` | S3 endpoint (for LocalStack) | - |

### Application Properties

Key configuration options in `application.yml`:

```yaml
aws:
  s3:
    bucket-name: vehicle-tracking-data
    endpoint: # Set for LocalStack testing

vehicle:
  location:
    retention-hours: 24
    cleanup-interval-minutes: 60
  video:
    url-expiry-hours: 24
    refresh-interval-minutes: 30

resilience4j:
  retry:
    instances:
      s3-operations:
        max-attempts: 3
        wait-duration: 1s
```

## Data Models

### Vehicle Data Structure
```json
{
  "vehicle_name": "TRUCK_001",
  "created_at": "2024-01-15T10:30:00",
  "last_updated": "2024-01-15T14:45:00",
  "current_location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "timestamp": "2024-01-15T14:45:00",
    "speed": 25.5,
    "heading": 180.0,
    "accuracy": 5.0
  },
  "past_24h_locations": [...],
  "live_video_data": {
    "url": "http://example.com/video/stream",
    "created_at": "2024-01-15T14:00:00",
    "expires_at": "2024-01-16T14:00:00",
    "quality": "HD",
    "provider": "video-service"
  },
  "status": "ACTIVE"
}
```

## Fault Tolerance

The service implements multiple layers of fault tolerance:

1. **Circuit Breakers**: Prevent cascading failures to S3
2. **Retries**: Automatic retry with exponential backoff
3. **Graceful Degradation**: Continue operation even if some features fail
4. **Health Checks**: Monitor service and dependency health
5. **Timeout Handling**: Prevent hanging requests

## Monitoring

### Metrics
- Application metrics via Micrometer
- Custom business metrics (vehicle count, location updates, etc.)
- JVM and system metrics

### Health Checks
- Database connectivity
- S3 service availability
- Application-specific health indicators

### Logging
- Structured logging with correlation IDs
- Different log levels for different environments
- Centralized logging support

## Cost Optimization

1. **S3 Storage**: Efficient JSON storage with compression
2. **Lifecycle Policies**: Automatic cleanup of old data
3. **Resource Pooling**: Connection pooling for AWS services
4. **Caching**: In-memory caching for frequently accessed data
5. **Batch Operations**: Bulk operations where possible

## Security

1. **Input Validation**: Comprehensive request validation
2. **Error Handling**: Secure error messages without sensitive data
3. **Authentication**: Ready for OAuth2/JWT integration
4. **HTTPS**: TLS encryption for all communications
5. **Secrets Management**: Environment-based configuration

## Deployment

### Production Deployment

1. **AWS ECS/EKS**: Container orchestration
2. **Application Load Balancer**: Traffic distribution
3. **Auto Scaling**: Based on CPU/memory metrics
4. **CloudWatch**: Monitoring and alerting
5. **S3**: Primary data storage

### Environment-Specific Configurations

- **Development**: LocalStack for AWS services
- **Testing**: TestContainers for integration tests
- **Staging**: Reduced resources, same architecture
- **Production**: Full redundancy and monitoring

## Troubleshooting

### Common Issues

1. **S3 Connection Issues**:
   ```bash
   # Check LocalStack status
   curl http://localhost:4566/_localstack/health
   
   # Verify bucket creation
   aws --endpoint-url=http://localhost:4566 s3 ls
   ```

2. **Application Won't Start**:
   ```bash
   # Check logs
   docker-compose logs vehicle-tracking-service
   
   # Verify environment variables
   docker-compose exec vehicle-tracking-service env | grep AWS
   ```

3. **Tests Failing**:
   ```bash
   # Clean and rebuild
   mvn clean compile
   
   # Run specific test
   mvn test -Dtest=VehicleServiceTest
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
