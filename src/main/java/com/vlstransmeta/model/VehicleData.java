package com.vlstransmeta.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Represents complete vehicle data stored in S3
 */
public class VehicleData {
    
    @JsonProperty("vehicle_name")
    private String vehicleName;
    
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonProperty("last_updated")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastUpdated;
    
    @JsonProperty("past_24h_locations")
    private List<LocationData> past24hLocations;
    
    @JsonProperty("live_video_data")
    private LiveVideoData liveVideoData;
    
    @JsonProperty("current_location")
    private LocationData currentLocation;
    
    @JsonProperty("status")
    private String status;

    public VehicleData() {
        this.past24hLocations = new ArrayList<>();
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "ACTIVE";
    }

    public VehicleData(String vehicleName) {
        this();
        this.vehicleName = vehicleName;
    }

    /**
     * Add a new location to the past 24h locations list
     * Automatically removes locations older than 24 hours
     */
    public void addLocation(LocationData location) {
        if (location == null) return;
        
        // Remove locations older than 24 hours
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24);
        past24hLocations.removeIf(loc -> loc.getTimestamp().isBefore(cutoff));
        
        // Add new location if it's not a duplicate
        if (!past24hLocations.contains(location)) {
            past24hLocations.add(location);
            this.currentLocation = location;
            this.lastUpdated = LocalDateTime.now();
        }
    }

    /**
     * Update live video data
     */
    public void updateLiveVideoData(LiveVideoData videoData) {
        this.liveVideoData = videoData;
        this.lastUpdated = LocalDateTime.now();
    }

    /**
     * Get the most recent location
     */
    public LocationData getMostRecentLocation() {
        if (past24hLocations.isEmpty()) {
            return currentLocation;
        }
        
        return past24hLocations.stream()
                .max((l1, l2) -> l1.getTimestamp().compareTo(l2.getTimestamp()))
                .orElse(currentLocation);
    }

    /**
     * Check if live video URL is expired or will expire soon
     */
    public boolean needsVideoUrlRefresh() {
        return liveVideoData == null || 
               liveVideoData.isExpired() || 
               liveVideoData.willExpireWithin(30);
    }

    // Getters and Setters
    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public List<LocationData> getPast24hLocations() {
        return past24hLocations;
    }

    public void setPast24hLocations(List<LocationData> past24hLocations) {
        this.past24hLocations = past24hLocations != null ? past24hLocations : new ArrayList<>();
    }

    public LiveVideoData getLiveVideoData() {
        return liveVideoData;
    }

    public void setLiveVideoData(LiveVideoData liveVideoData) {
        this.liveVideoData = liveVideoData;
    }

    public LocationData getCurrentLocation() {
        return currentLocation;
    }

    public void setCurrentLocation(LocationData currentLocation) {
        this.currentLocation = currentLocation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VehicleData that = (VehicleData) o;
        return Objects.equals(vehicleName, that.vehicleName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(vehicleName);
    }

    @Override
    public String toString() {
        return "VehicleData{" +
                "vehicleName='" + vehicleName + '\'' +
                ", createdAt=" + createdAt +
                ", lastUpdated=" + lastUpdated +
                ", locationsCount=" + (past24hLocations != null ? past24hLocations.size() : 0) +
                ", hasLiveVideo=" + (liveVideoData != null) +
                ", status='" + status + '\'' +
                '}';
    }
}
