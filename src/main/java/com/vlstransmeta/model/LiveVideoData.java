package com.vlstransmeta.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Represents live video URL data for a vehicle
 */
public class LiveVideoData {
    
    @JsonProperty("url")
    private String url;
    
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonProperty("expires_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime expiresAt;
    
    @JsonProperty("quality")
    private String quality;
    
    @JsonProperty("provider")
    private String provider;

    public LiveVideoData() {}

    public LiveVideoData(String url, LocalDateTime createdAt, LocalDateTime expiresAt) {
        this.url = url;
        this.createdAt = createdAt;
        this.expiresAt = expiresAt;
    }

    public LiveVideoData(String url, LocalDateTime createdAt, LocalDateTime expiresAt, 
                        String quality, String provider) {
        this.url = url;
        this.createdAt = createdAt;
        this.expiresAt = expiresAt;
        this.quality = quality;
        this.provider = provider;
    }

    /**
     * Check if the video URL has expired
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * Check if the video URL will expire within the given minutes
     */
    public boolean willExpireWithin(int minutes) {
        return LocalDateTime.now().plusMinutes(minutes).isAfter(expiresAt);
    }

    // Getters and Setters
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public String getQuality() {
        return quality;
    }

    public void setQuality(String quality) {
        this.quality = quality;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LiveVideoData that = (LiveVideoData) o;
        return Objects.equals(url, that.url) &&
               Objects.equals(createdAt, that.createdAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(url, createdAt);
    }

    @Override
    public String toString() {
        return "LiveVideoData{" +
                "url='" + url + '\'' +
                ", createdAt=" + createdAt +
                ", expiresAt=" + expiresAt +
                ", quality='" + quality + '\'' +
                ", provider='" + provider + '\'' +
                '}';
    }
}
