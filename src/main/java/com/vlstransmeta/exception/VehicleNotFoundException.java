package com.vlstransmeta.exception;

/**
 * Exception thrown when a vehicle is not found
 */
public class VehicleNotFoundException extends RuntimeException {
    
    private final String vehicleName;

    public VehicleNotFoundException(String vehicleName) {
        super("Vehicle not found: " + vehicleName);
        this.vehicleName = vehicleName;
    }

    public VehicleNotFoundException(String vehicleName, String message) {
        super(message);
        this.vehicleName = vehicleName;
    }

    public VehicleNotFoundException(String vehicleName, String message, Throwable cause) {
        super(message, cause);
        this.vehicleName = vehicleName;
    }

    public String getVehicleName() {
        return vehicleName;
    }
}
