package com.vlstransmeta.exception;

/**
 * Exception thrown when S3 operations fail
 */
public class S3OperationException extends RuntimeException {
    
    private final String operation;
    private final String bucketName;
    private final String key;

    public S3OperationException(String operation, String bucketName, String key, String message) {
        super(String.format("S3 %s operation failed for bucket '%s', key '%s': %s", 
                          operation, bucketName, key, message));
        this.operation = operation;
        this.bucketName = bucketName;
        this.key = key;
    }

    public S3OperationException(String operation, String bucketName, String key, String message, Throwable cause) {
        super(String.format("S3 %s operation failed for bucket '%s', key '%s': %s", 
                          operation, bucketName, key, message), cause);
        this.operation = operation;
        this.bucketName = bucketName;
        this.key = key;
    }

    public String getOperation() {
        return operation;
    }

    public String getBucketName() {
        return bucketName;
    }

    public String getKey() {
        return key;
    }
}
