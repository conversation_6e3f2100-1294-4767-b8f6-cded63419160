package com.vlstransmeta.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vlstransmeta.exception.S3OperationException;
import com.vlstransmeta.model.VehicleData;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.IOException;
import java.util.Optional;

/**
 * Service for S3 operations with fault tolerance
 */
@Service
public class S3Service {
    
    private static final Logger logger = LoggerFactory.getLogger(S3Service.class);
    
    private final S3Client s3Client;
    private final ObjectMapper objectMapper;
    private final String bucketName;

    @Autowired
    public S3Service(S3Client s3Client, ObjectMapper objectMapper,
                     @Value("${aws.s3.bucket-name}") String bucketName) {
        this.s3Client = s3Client;
        this.objectMapper = objectMapper;
        this.bucketName = bucketName;
        
        // Ensure bucket exists
        createBucketIfNotExists();
    }

    /**
     * Create S3 bucket if it doesn't exist
     */
    private void createBucketIfNotExists() {
        try {
            HeadBucketRequest headBucketRequest = HeadBucketRequest.builder()
                    .bucket(bucketName)
                    .build();
            
            s3Client.headBucket(headBucketRequest);
            logger.info("S3 bucket '{}' exists", bucketName);
        } catch (NoSuchBucketException e) {
            logger.info("Creating S3 bucket '{}'", bucketName);
            try {
                CreateBucketRequest createBucketRequest = CreateBucketRequest.builder()
                        .bucket(bucketName)
                        .build();
                
                s3Client.createBucket(createBucketRequest);
                logger.info("Successfully created S3 bucket '{}'", bucketName);
            } catch (Exception createException) {
                logger.error("Failed to create S3 bucket '{}'", bucketName, createException);
                throw new S3OperationException("CREATE_BUCKET", bucketName, "", 
                        "Failed to create bucket", createException);
            }
        } catch (Exception e) {
            logger.error("Error checking S3 bucket '{}'", bucketName, e);
            throw new S3OperationException("HEAD_BUCKET", bucketName, "", 
                    "Failed to check bucket existence", e);
        }
    }

    /**
     * Save vehicle data to S3
     */
    @Retry(name = "s3-operations")
    @CircuitBreaker(name = "s3-operations")
    public void saveVehicleData(VehicleData vehicleData) {
        String key = generateVehicleKey(vehicleData.getVehicleName());
        
        try {
            String jsonData = objectMapper.writeValueAsString(vehicleData);
            
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType("application/json")
                    .metadata(java.util.Map.of(
                            "vehicle-name", vehicleData.getVehicleName(),
                            "last-updated", vehicleData.getLastUpdated().toString()
                    ))
                    .build();
            
            s3Client.putObject(putRequest, RequestBody.fromString(jsonData));
            
            logger.debug("Successfully saved vehicle data for '{}' to S3", vehicleData.getVehicleName());
            
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize vehicle data for '{}'", vehicleData.getVehicleName(), e);
            throw new S3OperationException("PUT", bucketName, key, 
                    "Failed to serialize vehicle data", e);
        } catch (Exception e) {
            logger.error("Failed to save vehicle data for '{}' to S3", vehicleData.getVehicleName(), e);
            throw new S3OperationException("PUT", bucketName, key, 
                    "Failed to save vehicle data", e);
        }
    }

    /**
     * Load vehicle data from S3
     */
    @Retry(name = "s3-operations")
    @CircuitBreaker(name = "s3-operations")
    public Optional<VehicleData> loadVehicleData(String vehicleName) {
        String key = generateVehicleKey(vehicleName);
        
        try {
            GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();
            
            String jsonData = s3Client.getObjectAsBytes(getRequest).asUtf8String();
            VehicleData vehicleData = objectMapper.readValue(jsonData, VehicleData.class);
            
            logger.debug("Successfully loaded vehicle data for '{}' from S3", vehicleName);
            return Optional.of(vehicleData);
            
        } catch (NoSuchKeyException e) {
            logger.debug("Vehicle data not found for '{}' in S3", vehicleName);
            return Optional.empty();
        } catch (IOException e) {
            logger.error("Failed to deserialize vehicle data for '{}'", vehicleName, e);
            throw new S3OperationException("GET", bucketName, key, 
                    "Failed to deserialize vehicle data", e);
        } catch (Exception e) {
            logger.error("Failed to load vehicle data for '{}' from S3", vehicleName, e);
            throw new S3OperationException("GET", bucketName, key, 
                    "Failed to load vehicle data", e);
        }
    }

    /**
     * Check if vehicle exists in S3
     */
    @Retry(name = "s3-operations")
    @CircuitBreaker(name = "s3-operations")
    public boolean vehicleExists(String vehicleName) {
        String key = generateVehicleKey(vehicleName);
        
        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();
            
            s3Client.headObject(headRequest);
            return true;
            
        } catch (NoSuchKeyException e) {
            return false;
        } catch (Exception e) {
            logger.error("Failed to check if vehicle '{}' exists in S3", vehicleName, e);
            throw new S3OperationException("HEAD", bucketName, key, 
                    "Failed to check vehicle existence", e);
        }
    }

    /**
     * Delete vehicle data from S3
     */
    @Retry(name = "s3-operations")
    @CircuitBreaker(name = "s3-operations")
    public void deleteVehicleData(String vehicleName) {
        String key = generateVehicleKey(vehicleName);
        
        try {
            DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();
            
            s3Client.deleteObject(deleteRequest);
            
            logger.info("Successfully deleted vehicle data for '{}' from S3", vehicleName);
            
        } catch (Exception e) {
            logger.error("Failed to delete vehicle data for '{}' from S3", vehicleName, e);
            throw new S3OperationException("DELETE", bucketName, key, 
                    "Failed to delete vehicle data", e);
        }
    }

    /**
     * Generate S3 key for vehicle data
     */
    private String generateVehicleKey(String vehicleName) {
        // Sanitize vehicle name for S3 key
        String sanitizedName = vehicleName.replaceAll("[^a-zA-Z0-9-_.]", "_");
        return String.format("vehicles/%s/data.json", sanitizedName);
    }

    /**
     * Get bucket name
     */
    public String getBucketName() {
        return bucketName;
    }
}
