package com.vlstransmeta.service;

import com.vlstransmeta.model.LocationData;
import com.vlstransmeta.model.VehicleData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for location-related operations
 */
@Service
public class LocationService {
    
    private static final Logger logger = LoggerFactory.getLogger(LocationService.class);
    
    // Threshold for considering two locations as duplicates (in meters)
    private static final double DUPLICATE_THRESHOLD_METERS = 10.0;
    
    // Threshold for considering two timestamps as duplicates (in seconds)
    private static final long DUPLICATE_TIME_THRESHOLD_SECONDS = 30;

    /**
     * Check if the new location is a duplicate of the last location
     */
    public boolean isDuplicateLocation(VehicleData vehicleData, LocationData newLocation) {
        LocationData lastLocation = vehicleData.getMostRecentLocation();
        
        if (lastLocation == null) {
            return false;
        }
        
        // Check time difference
        long timeDifference = Math.abs(
                java.time.Duration.between(lastLocation.getTimestamp(), newLocation.getTimestamp()).getSeconds()
        );
        
        if (timeDifference < DUPLICATE_TIME_THRESHOLD_SECONDS) {
            // Check distance difference
            double distance = calculateDistance(
                    lastLocation.getLatitude(), lastLocation.getLongitude(),
                    newLocation.getLatitude(), newLocation.getLongitude()
            );
            
            if (distance < DUPLICATE_THRESHOLD_METERS) {
                logger.debug("Duplicate location detected for vehicle '{}': distance={}m, time={}s", 
                           vehicleData.getVehicleName(), distance, timeDifference);
                return true;
            }
        }
        
        return false;
    }

    /**
     * Filter locations to only include those from the past 24 hours
     */
    public List<LocationData> filterPast24Hours(List<LocationData> locations) {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24);
        
        return locations.stream()
                .filter(location -> location.getTimestamp().isAfter(cutoff))
                .sorted((l1, l2) -> l1.getTimestamp().compareTo(l2.getTimestamp()))
                .toList();
    }

    /**
     * Calculate distance between two coordinates using Haversine formula
     */
    public double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // Earth's radius in meters
        
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLonRad = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c; // Distance in meters
    }

    /**
     * Validate location data
     */
    public boolean isValidLocation(LocationData location) {
        if (location == null) {
            return false;
        }
        
        Double lat = location.getLatitude();
        Double lon = location.getLongitude();
        
        if (lat == null || lon == null) {
            return false;
        }
        
        // Check latitude bounds
        if (lat < -90.0 || lat > 90.0) {
            return false;
        }
        
        // Check longitude bounds
        if (lon < -180.0 || lon > 180.0) {
            return false;
        }
        
        // Check if timestamp is reasonable (not too far in the future or past)
        if (location.getTimestamp() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime oneHourAgo = now.minusHours(1);
            LocalDateTime oneHourFromNow = now.plusHours(1);
            
            if (location.getTimestamp().isBefore(oneHourAgo) || 
                location.getTimestamp().isAfter(oneHourFromNow)) {
                logger.warn("Location timestamp is outside reasonable range: {}", location.getTimestamp());
                // Don't reject, but log warning
            }
        }
        
        return true;
    }

    /**
     * Calculate average speed between two locations
     */
    public double calculateAverageSpeed(LocationData from, LocationData to) {
        if (from == null || to == null || from.getTimestamp() == null || to.getTimestamp() == null) {
            return 0.0;
        }
        
        double distance = calculateDistance(
                from.getLatitude(), from.getLongitude(),
                to.getLatitude(), to.getLongitude()
        );
        
        long timeDifferenceSeconds = Math.abs(
                java.time.Duration.between(from.getTimestamp(), to.getTimestamp()).getSeconds()
        );
        
        if (timeDifferenceSeconds == 0) {
            return 0.0;
        }
        
        // Return speed in m/s
        return distance / timeDifferenceSeconds;
    }
}
