package com.vlstransmeta.service;

import com.vlstransmeta.dto.LocationUpdateRequest;
import com.vlstransmeta.dto.VideoUrlUpdateRequest;
import com.vlstransmeta.dto.VehicleResponse;
import com.vlstransmeta.exception.VehicleNotFoundException;
import com.vlstransmeta.model.LocationData;
import com.vlstransmeta.model.LiveVideoData;
import com.vlstransmeta.model.VehicleData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Core service for vehicle operations
 */
@Service
public class VehicleService {
    
    private static final Logger logger = LoggerFactory.getLogger(VehicleService.class);
    
    private final S3Service s3Service;
    private final LocationService locationService;
    private final VideoService videoService;

    @Autowired
    public VehicleService(S3Service s3Service, LocationService locationService, VideoService videoService) {
        this.s3Service = s3Service;
        this.locationService = locationService;
        this.videoService = videoService;
    }

    /**
     * Create or get existing vehicle
     */
    public VehicleData createOrGetVehicle(String vehicleName) {
        if (vehicleName == null || vehicleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Vehicle name cannot be null or empty");
        }
        
        String normalizedName = vehicleName.trim();
        
        // Try to load existing vehicle data
        Optional<VehicleData> existingVehicle = s3Service.loadVehicleData(normalizedName);
        
        if (existingVehicle.isPresent()) {
            logger.info("Found existing vehicle: {}", normalizedName);
            return existingVehicle.get();
        }
        
        // Create new vehicle
        VehicleData newVehicle = new VehicleData(normalizedName);
        s3Service.saveVehicleData(newVehicle);
        
        logger.info("Created new vehicle: {}", normalizedName);
        return newVehicle;
    }

    /**
     * Get vehicle data
     */
    public VehicleResponse getVehicle(String vehicleName) {
        VehicleData vehicleData = getVehicleData(vehicleName);
        
        return new VehicleResponse(
                vehicleData.getVehicleName(),
                vehicleData.getCurrentLocation(),
                vehicleData.getPast24hLocations(),
                vehicleData.getLiveVideoData(),
                vehicleData.getLastUpdated(),
                vehicleData.getStatus()
        );
    }

    /**
     * Update vehicle location
     */
    public VehicleResponse updateLocation(LocationUpdateRequest request) {
        VehicleData vehicleData = createOrGetVehicle(request.getVehicleName());
        
        // Create location data
        LocationData locationData = new LocationData(
                request.getLatitude(),
                request.getLongitude(),
                request.getTimestamp() != null ? request.getTimestamp() : LocalDateTime.now(),
                request.getSpeed(),
                request.getHeading(),
                request.getAccuracy()
        );
        
        // Check if this is a duplicate location
        if (!locationService.isDuplicateLocation(vehicleData, locationData)) {
            vehicleData.addLocation(locationData);
            s3Service.saveVehicleData(vehicleData);
            
            logger.info("Updated location for vehicle '{}': lat={}, lon={}", 
                       request.getVehicleName(), request.getLatitude(), request.getLongitude());
        } else {
            logger.debug("Skipped duplicate location for vehicle '{}'", request.getVehicleName());
        }
        
        return new VehicleResponse(
                vehicleData.getVehicleName(),
                vehicleData.getCurrentLocation(),
                vehicleData.getPast24hLocations(),
                vehicleData.getLiveVideoData(),
                vehicleData.getLastUpdated(),
                vehicleData.getStatus()
        );
    }

    /**
     * Update vehicle live video URL
     */
    public VehicleResponse updateVideoUrl(VideoUrlUpdateRequest request) {
        VehicleData vehicleData = createOrGetVehicle(request.getVehicleName());
        
        // Create live video data
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = now.plusHours(request.getExpiryHours());
        
        LiveVideoData videoData = new LiveVideoData(
                request.getVideoUrl(),
                now,
                expiresAt,
                request.getQuality(),
                request.getProvider()
        );
        
        vehicleData.updateLiveVideoData(videoData);
        s3Service.saveVehicleData(vehicleData);
        
        logger.info("Updated video URL for vehicle '{}', expires at: {}", 
                   request.getVehicleName(), expiresAt);
        
        return new VehicleResponse(
                vehicleData.getVehicleName(),
                vehicleData.getCurrentLocation(),
                vehicleData.getPast24hLocations(),
                vehicleData.getLiveVideoData(),
                vehicleData.getLastUpdated(),
                vehicleData.getStatus()
        );
    }

    /**
     * Refresh expired video URLs
     */
    public void refreshExpiredVideoUrls() {
        // This would typically iterate through all vehicles
        // For now, it's a placeholder for the scheduled task
        logger.info("Checking for expired video URLs...");
        // Implementation would depend on how you want to list all vehicles
        // Could use S3 list operations or maintain a separate index
    }

    /**
     * Clean up old location data
     */
    public void cleanupOldLocationData() {
        logger.info("Cleaning up old location data...");
        // Similar to refreshExpiredVideoUrls, this would iterate through vehicles
        // and clean up locations older than 24 hours
    }

    /**
     * Get vehicle data or throw exception if not found
     */
    private VehicleData getVehicleData(String vehicleName) {
        return s3Service.loadVehicleData(vehicleName)
                .orElseThrow(() -> new VehicleNotFoundException(vehicleName));
    }

    /**
     * Check if vehicle exists
     */
    public boolean vehicleExists(String vehicleName) {
        return s3Service.vehicleExists(vehicleName);
    }

    /**
     * Delete vehicle
     */
    public void deleteVehicle(String vehicleName) {
        if (!vehicleExists(vehicleName)) {
            throw new VehicleNotFoundException(vehicleName);
        }
        
        s3Service.deleteVehicleData(vehicleName);
        logger.info("Deleted vehicle: {}", vehicleName);
    }
}
