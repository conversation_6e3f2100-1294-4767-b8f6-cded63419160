package com.vlstransmeta;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

/**
 * Main application class for Vehicle Tracking Service
 */
@SpringBootApplication
public class VehicleTrackingApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(VehicleTrackingApplication.class);

    public static void main(String[] args) {
        logger.info("Starting Vehicle Tracking Service...");
        SpringApplication.run(VehicleTrackingApplication.class, args);
        logger.info("Vehicle Tracking Service started successfully");
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
