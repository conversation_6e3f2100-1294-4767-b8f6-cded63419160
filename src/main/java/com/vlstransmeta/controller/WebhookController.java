package com.vlstransmeta.controller;

import com.vlstransmeta.dto.LocationUpdateRequest;
import com.vlstransmeta.dto.VideoUrlUpdateRequest;
import com.vlstransmeta.dto.VehicleResponse;
import com.vlstransmeta.service.VehicleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Webhook controller for receiving external updates
 */
@RestController
@RequestMapping("/webhooks")
@Tag(name = "Webhooks", description = "Webhook endpoints for receiving location and video URL updates")
public class WebhookController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebhookController.class);
    
    private final VehicleService vehicleService;

    @Autowired
    public WebhookController(VehicleService vehicleService) {
        this.vehicleService = vehicleService;
    }

    @Operation(summary = "Location update webhook", 
               description = "Receives location updates from external systems and updates vehicle tracking data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Location updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid location data"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/location")
    public ResponseEntity<VehicleResponse> updateLocation(@Valid @RequestBody LocationUpdateRequest request) {
        logger.info("Received location update webhook for vehicle '{}': lat={}, lon={}", 
                   request.getVehicleName(), request.getLatitude(), request.getLongitude());
        
        VehicleResponse response = vehicleService.updateLocation(request);
        
        logger.info("Successfully processed location update for vehicle '{}'", request.getVehicleName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Video URL update webhook", 
               description = "Receives live video URL updates from external video services")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Video URL updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid video URL data"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/video-url")
    public ResponseEntity<VehicleResponse> updateVideoUrl(@Valid @RequestBody VideoUrlUpdateRequest request) {
        logger.info("Received video URL update webhook for vehicle '{}': url={}", 
                   request.getVehicleName(), request.getVideoUrl());
        
        VehicleResponse response = vehicleService.updateVideoUrl(request);
        
        logger.info("Successfully processed video URL update for vehicle '{}'", request.getVehicleName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Generic webhook", 
               description = "Generic webhook endpoint for handling various types of updates")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Webhook processed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid webhook data"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/generic")
    public ResponseEntity<Map<String, Object>> handleGenericWebhook(@RequestBody Map<String, Object> payload) {
        logger.info("Received generic webhook with payload keys: {}", payload.keySet());
        
        try {
            // Process different types of webhooks based on payload content
            String type = (String) payload.get("type");
            String vehicleName = (String) payload.get("vehicle_name");
            
            if (vehicleName == null || vehicleName.trim().isEmpty()) {
                throw new IllegalArgumentException("vehicle_name is required in webhook payload");
            }
            
            switch (type != null ? type.toLowerCase() : "") {
                case "location":
                    return handleLocationWebhook(payload);
                case "video":
                case "video_url":
                    return handleVideoWebhook(payload);
                default:
                    logger.warn("Unknown webhook type '{}' for vehicle '{}'", type, vehicleName);
                    return ResponseEntity.ok(Map.of(
                            "status", "received",
                            "message", "Webhook received but not processed - unknown type",
                            "type", type != null ? type : "unknown"
                    ));
            }
            
        } catch (Exception e) {
            logger.error("Error processing generic webhook", e);
            return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", e.getMessage()
            ));
        }
    }

    /**
     * Handle location webhook from generic payload
     */
    private ResponseEntity<Map<String, Object>> handleLocationWebhook(Map<String, Object> payload) {
        try {
            LocationUpdateRequest request = new LocationUpdateRequest();
            request.setVehicleName((String) payload.get("vehicle_name"));
            request.setLatitude(((Number) payload.get("latitude")).doubleValue());
            request.setLongitude(((Number) payload.get("longitude")).doubleValue());
            
            if (payload.get("speed") != null) {
                request.setSpeed(((Number) payload.get("speed")).doubleValue());
            }
            if (payload.get("heading") != null) {
                request.setHeading(((Number) payload.get("heading")).doubleValue());
            }
            if (payload.get("accuracy") != null) {
                request.setAccuracy(((Number) payload.get("accuracy")).doubleValue());
            }
            
            VehicleResponse response = vehicleService.updateLocation(request);
            
            return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "Location updated successfully",
                    "vehicle", response
            ));
            
        } catch (Exception e) {
            logger.error("Error processing location webhook", e);
            throw new IllegalArgumentException("Invalid location data in webhook payload: " + e.getMessage());
        }
    }

    /**
     * Handle video webhook from generic payload
     */
    private ResponseEntity<Map<String, Object>> handleVideoWebhook(Map<String, Object> payload) {
        try {
            VideoUrlUpdateRequest request = new VideoUrlUpdateRequest();
            request.setVehicleName((String) payload.get("vehicle_name"));
            request.setVideoUrl((String) payload.get("video_url"));
            request.setQuality((String) payload.get("quality"));
            request.setProvider((String) payload.get("provider"));
            
            if (payload.get("expiry_hours") != null) {
                request.setExpiryHours(((Number) payload.get("expiry_hours")).intValue());
            }
            
            VehicleResponse response = vehicleService.updateVideoUrl(request);
            
            return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "Video URL updated successfully",
                    "vehicle", response
            ));
            
        } catch (Exception e) {
            logger.error("Error processing video webhook", e);
            throw new IllegalArgumentException("Invalid video data in webhook payload: " + e.getMessage());
        }
    }

    @Operation(summary = "Webhook health check", description = "Health check endpoint for webhook service")
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> webhookHealth() {
        return ResponseEntity.ok(Map.of(
                "status", "healthy",
                "service", "webhook",
                "timestamp", System.currentTimeMillis()
        ));
    }
}
