package com.vlstransmeta.controller;

import com.vlstransmeta.dto.VehicleResponse;
import com.vlstransmeta.service.VehicleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for vehicle operations
 */
@RestController
@RequestMapping("/vehicles")
@Tag(name = "Vehicle Management", description = "APIs for managing vehicle data and tracking")
public class VehicleController {
    
    private static final Logger logger = LoggerFactory.getLogger(VehicleController.class);
    
    private final VehicleService vehicleService;

    @Autowired
    public VehicleController(VehicleService vehicleService) {
        this.vehicleService = vehicleService;
    }

    @Operation(summary = "Create or get vehicle", description = "Creates a new vehicle if it doesn't exist, otherwise returns existing vehicle data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Vehicle data retrieved successfully"),
            @ApiResponse(responseCode = "201", description = "New vehicle created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid vehicle name"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/{vehicleName}")
    public ResponseEntity<VehicleResponse> createOrGetVehicle(
            @Parameter(description = "Name of the vehicle", required = true)
            @PathVariable String vehicleName) {
        
        logger.info("Request to create or get vehicle: {}", vehicleName);
        
        boolean existed = vehicleService.vehicleExists(vehicleName);
        VehicleResponse response = vehicleService.getVehicle(vehicleName);
        
        if (existed) {
            logger.info("Returned existing vehicle: {}", vehicleName);
            return ResponseEntity.ok(response);
        } else {
            logger.info("Created new vehicle: {}", vehicleName);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        }
    }

    @Operation(summary = "Get vehicle data", description = "Retrieves complete vehicle data including location history and live video URL")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Vehicle data retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Vehicle not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{vehicleName}")
    public ResponseEntity<VehicleResponse> getVehicle(
            @Parameter(description = "Name of the vehicle", required = true)
            @PathVariable String vehicleName) {
        
        logger.info("Request to get vehicle: {}", vehicleName);
        
        VehicleResponse response = vehicleService.getVehicle(vehicleName);
        
        logger.info("Retrieved vehicle data for: {}", vehicleName);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Check if vehicle exists", description = "Checks if a vehicle exists in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Vehicle exists"),
            @ApiResponse(responseCode = "404", description = "Vehicle not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{vehicleName}/exists")
    public ResponseEntity<Boolean> vehicleExists(
            @Parameter(description = "Name of the vehicle", required = true)
            @PathVariable String vehicleName) {
        
        logger.debug("Request to check if vehicle exists: {}", vehicleName);
        
        boolean exists = vehicleService.vehicleExists(vehicleName);
        
        if (exists) {
            return ResponseEntity.ok(true);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(false);
        }
    }

    @Operation(summary = "Delete vehicle", description = "Deletes a vehicle and all its data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Vehicle deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Vehicle not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @DeleteMapping("/{vehicleName}")
    public ResponseEntity<Void> deleteVehicle(
            @Parameter(description = "Name of the vehicle", required = true)
            @PathVariable String vehicleName) {
        
        logger.info("Request to delete vehicle: {}", vehicleName);
        
        vehicleService.deleteVehicle(vehicleName);
        
        logger.info("Deleted vehicle: {}", vehicleName);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Health check", description = "Simple health check endpoint")
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Vehicle service is healthy");
    }
}
