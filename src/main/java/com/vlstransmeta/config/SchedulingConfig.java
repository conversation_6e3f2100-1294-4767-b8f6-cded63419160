package com.vlstransmeta.config;

import com.vlstransmeta.service.VehicleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * Configuration for scheduled tasks
 */
@Configuration
@EnableScheduling
public class SchedulingConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(SchedulingConfig.class);
    
    private final VehicleService vehicleService;

    @Autowired
    public SchedulingConfig(VehicleService vehicleService) {
        this.vehicleService = vehicleService;
    }

    /**
     * Clean up old location data every hour
     * Removes location data older than 24 hours
     */
    @Scheduled(fixedRate = 3600000) // 1 hour = 3600000 ms
    public void cleanupOldLocationData() {
        try {
            logger.info("Starting scheduled cleanup of old location data");
            vehicleService.cleanupOldLocationData();
            logger.info("Completed scheduled cleanup of old location data");
        } catch (Exception e) {
            logger.error("Error during scheduled cleanup of old location data", e);
        }
    }

    /**
     * Refresh expired video URLs every 30 minutes
     * Checks for URLs that have expired or will expire soon
     */
    @Scheduled(fixedRate = 1800000) // 30 minutes = 1800000 ms
    public void refreshExpiredVideoUrls() {
        try {
            logger.info("Starting scheduled refresh of expired video URLs");
            vehicleService.refreshExpiredVideoUrls();
            logger.info("Completed scheduled refresh of expired video URLs");
        } catch (Exception e) {
            logger.error("Error during scheduled refresh of expired video URLs", e);
        }
    }

    /**
     * Health check log every 5 minutes
     * Simple periodic health check to ensure the service is running
     */
    @Scheduled(fixedRate = 300000) // 5 minutes = 300000 ms
    public void healthCheck() {
        logger.debug("Scheduled health check - Vehicle tracking service is running");
    }
}
