package com.vlstransmeta.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;

import java.net.URI;

/**
 * AWS configuration for S3 client
 */
@Configuration
public class AwsConfig {

    @Value("${aws.region:us-east-1}")
    private String region;

    @Value("${aws.s3.endpoint:}")
    private String s3Endpoint;

    @Value("${aws.credentials.access-key:}")
    private String accessKey;

    @Value("${aws.credentials.secret-key:}")
    private String secretKey;

    @Bean
    public S3Client s3Client() {
        S3ClientBuilder builder = S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(awsCredentialsProvider());

        // For LocalStack or custom S3 endpoint
        if (s3Endpoint != null && !s3Endpoint.trim().isEmpty()) {
            builder.endpointOverride(URI.create(s3Endpoint))
                   .forcePathStyle(true); // Required for LocalStack
        }

        return builder.build();
    }

    @Bean
    public AwsCredentialsProvider awsCredentialsProvider() {
        // Use provided credentials if available, otherwise use default provider chain
        if (accessKey != null && !accessKey.trim().isEmpty() && 
            secretKey != null && !secretKey.trim().isEmpty()) {
            return StaticCredentialsProvider.create(
                    AwsBasicCredentials.create(accessKey, secretKey)
            );
        }
        
        // Use default credentials provider chain (IAM roles, environment variables, etc.)
        return DefaultCredentialsProvider.create();
    }
}
