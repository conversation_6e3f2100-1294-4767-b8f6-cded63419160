package com.vlstransmeta.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.vlstransmeta.model.LocationData;
import com.vlstransmeta.model.LiveVideoData;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for vehicle data API
 */
public class VehicleResponse {
    
    @JsonProperty("vehicle_name")
    private String vehicleName;
    
    @JsonProperty("current_location")
    private LocationData currentLocation;
    
    @JsonProperty("past_24h_locations")
    private List<LocationData> past24hLocations;
    
    @JsonProperty("live_video_url")
    private String liveVideoUrl;
    
    @JsonProperty("video_expires_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime videoExpiresAt;
    
    @JsonProperty("last_updated")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastUpdated;
    
    @JsonProperty("status")
    private String status;

    public VehicleResponse() {}

    public VehicleResponse(String vehicleName, LocationData currentLocation, 
                          List<LocationData> past24hLocations, LiveVideoData liveVideoData,
                          LocalDateTime lastUpdated, String status) {
        this.vehicleName = vehicleName;
        this.currentLocation = currentLocation;
        this.past24hLocations = past24hLocations;
        this.lastUpdated = lastUpdated;
        this.status = status;
        
        if (liveVideoData != null) {
            this.liveVideoUrl = liveVideoData.getUrl();
            this.videoExpiresAt = liveVideoData.getExpiresAt();
        }
    }

    // Getters and Setters
    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public LocationData getCurrentLocation() {
        return currentLocation;
    }

    public void setCurrentLocation(LocationData currentLocation) {
        this.currentLocation = currentLocation;
    }

    public List<LocationData> getPast24hLocations() {
        return past24hLocations;
    }

    public void setPast24hLocations(List<LocationData> past24hLocations) {
        this.past24hLocations = past24hLocations;
    }

    public String getLiveVideoUrl() {
        return liveVideoUrl;
    }

    public void setLiveVideoUrl(String liveVideoUrl) {
        this.liveVideoUrl = liveVideoUrl;
    }

    public LocalDateTime getVideoExpiresAt() {
        return videoExpiresAt;
    }

    public void setVideoExpiresAt(LocalDateTime videoExpiresAt) {
        this.videoExpiresAt = videoExpiresAt;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
