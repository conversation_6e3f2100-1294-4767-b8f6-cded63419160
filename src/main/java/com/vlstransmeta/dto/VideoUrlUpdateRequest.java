package com.vlstransmeta.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Request DTO for video URL updates via webhook
 */
public class VideoUrlUpdateRequest {
    
    @JsonProperty("vehicle_name")
    @NotNull(message = "Vehicle name is required")
    @NotBlank(message = "Vehicle name cannot be blank")
    private String vehicleName;
    
    @JsonProperty("video_url")
    @NotNull(message = "Video URL is required")
    @NotBlank(message = "Video URL cannot be blank")
    private String videoUrl;
    
    @JsonProperty("quality")
    private String quality;
    
    @JsonProperty("provider")
    private String provider;
    
    @JsonProperty("expiry_hours")
    private Integer expiryHours;

    public VideoUrlUpdateRequest() {}

    public VideoUrlUpdateRequest(String vehicleName, String videoUrl) {
        this.vehicleName = vehicleName;
        this.videoUrl = videoUrl;
        this.expiryHours = 24; // Default 24 hours
    }

    // Getters and Setters
    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getQuality() {
        return quality;
    }

    public void setQuality(String quality) {
        this.quality = quality;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public Integer getExpiryHours() {
        return expiryHours != null ? expiryHours : 24;
    }

    public void setExpiryHours(Integer expiryHours) {
        this.expiryHours = expiryHours;
    }
}
