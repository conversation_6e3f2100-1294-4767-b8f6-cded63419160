# Docker-specific configuration
aws:
  region: us-east-1
  s3:
    bucket-name: vehicle-tracking-data
    endpoint: http://localstack:4566
  credentials:
    access-key: test
    secret-key: test

# Logging for Docker environment
logging:
  level:
    com.vlstransmeta: INFO
    software.amazon.awssdk: WARN
  file:
    name: /app/logs/vehicle-tracking.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
