server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: vehicle-tracking-service
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# AWS Configuration
aws:
  region: us-east-1
  s3:
    bucket-name: vehicle-tracking-data
    endpoint: # Leave empty for production, set for LocalStack testing
  credentials:
    access-key: ${AWS_ACCESS_KEY_ID:}
    secret-key: ${AWS_SECRET_ACCESS_KEY:}

# Vehicle Tracking Configuration
vehicle:
  location:
    retention-hours: 24
    cleanup-interval-minutes: 60
  video:
    url-expiry-hours: 24
    refresh-interval-minutes: 30

# Resilience4j Configuration
resilience4j:
  retry:
    instances:
      s3-operations:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
  circuit-breaker:
    instances:
      s3-operations:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Logging
logging:
  level:
    com.vlstransmeta: DEBUG
    software.amazon.awssdk: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
