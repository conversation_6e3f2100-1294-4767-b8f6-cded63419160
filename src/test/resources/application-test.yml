# Test configuration
aws:
  region: us-east-1
  s3:
    bucket-name: test-vehicle-tracking-data
    endpoint: http://localhost:4566
  credentials:
    access-key: test
    secret-key: test

# Test-specific settings
vehicle:
  location:
    retention-hours: 1  # Shorter retention for tests
    cleanup-interval-minutes: 1
  video:
    url-expiry-hours: 1
    refresh-interval-minutes: 1

# Disable scheduling in tests
spring:
  task:
    scheduling:
      enabled: false

# Logging for tests
logging:
  level:
    com.vlstransmeta: DEBUG
    software.amazon.awssdk: WARN
    org.testcontainers: INFO
