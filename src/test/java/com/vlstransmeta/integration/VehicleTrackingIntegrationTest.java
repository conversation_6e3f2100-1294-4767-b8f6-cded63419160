package com.vlstransmeta.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vlstransmeta.dto.LocationUpdateRequest;
import com.vlstransmeta.dto.VideoUrlUpdateRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.time.LocalDateTime;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class VehicleTrackingIntegrationTest {

    @Container
    static LocalStackContainer localstack = new LocalStackContainer(DockerImageName.parse("localstack/localstack:3.0"))
            .withServices(LocalStackContainer.Service.S3);

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("aws.s3.endpoint", localstack::getEndpoint);
        registry.add("aws.credentials.access-key", () -> "test");
        registry.add("aws.credentials.secret-key", () -> "test");
        registry.add("aws.region", () -> "us-east-1");
    }

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void testCompleteVehicleWorkflow() throws Exception {
        String vehicleName = "INTEGRATION_TEST_VEHICLE";

        // 1. Create vehicle
        mockMvc.perform(post("/vehicles/" + vehicleName))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.vehicle_name").value(vehicleName))
                .andExpect(jsonPath("$.status").value("ACTIVE"));

        // 2. Check vehicle exists
        mockMvc.perform(get("/vehicles/" + vehicleName + "/exists"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));

        // 3. Get vehicle data
        mockMvc.perform(get("/vehicles/" + vehicleName))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.vehicle_name").value(vehicleName));

        // 4. Update location via webhook
        LocationUpdateRequest locationRequest = new LocationUpdateRequest();
        locationRequest.setVehicleName(vehicleName);
        locationRequest.setLatitude(40.7128);
        locationRequest.setLongitude(-74.0060);
        locationRequest.setTimestamp(LocalDateTime.now());

        mockMvc.perform(post("/webhooks/location")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(locationRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.vehicle_name").value(vehicleName))
                .andExpect(jsonPath("$.current_location.latitude").value(40.7128))
                .andExpect(jsonPath("$.current_location.longitude").value(-74.0060));

        // 5. Update video URL via webhook
        VideoUrlUpdateRequest videoRequest = new VideoUrlUpdateRequest();
        videoRequest.setVehicleName(vehicleName);
        videoRequest.setVideoUrl("http://example.com/video/stream");
        videoRequest.setExpiryHours(24);

        mockMvc.perform(post("/webhooks/video-url")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(videoRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.vehicle_name").value(vehicleName))
                .andExpect(jsonPath("$.live_video_url").value("http://example.com/video/stream"));

        // 6. Verify updated vehicle data
        mockMvc.perform(get("/vehicles/" + vehicleName))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.vehicle_name").value(vehicleName))
                .andExpect(jsonPath("$.current_location.latitude").value(40.7128))
                .andExpect(jsonPath("$.live_video_url").value("http://example.com/video/stream"));

        // 7. Delete vehicle
        mockMvc.perform(delete("/vehicles/" + vehicleName))
                .andExpect(status().isNoContent());

        // 8. Verify vehicle is deleted
        mockMvc.perform(get("/vehicles/" + vehicleName + "/exists"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("false"));
    }

    @Test
    void testGenericWebhook() throws Exception {
        String vehicleName = "WEBHOOK_TEST_VEHICLE";

        // Create vehicle first
        mockMvc.perform(post("/vehicles/" + vehicleName))
                .andExpect(status().isCreated());

        // Test location webhook via generic endpoint
        String locationPayload = """
                {
                    "type": "location",
                    "vehicle_name": "%s",
                    "latitude": 37.7749,
                    "longitude": -122.4194,
                    "speed": 25.5,
                    "heading": 180.0
                }
                """.formatted(vehicleName);

        mockMvc.perform(post("/webhooks/generic")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(locationPayload))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.vehicle.vehicle_name").value(vehicleName));

        // Test video webhook via generic endpoint
        String videoPayload = """
                {
                    "type": "video",
                    "vehicle_name": "%s",
                    "video_url": "http://example.com/video/stream2",
                    "quality": "HD",
                    "provider": "test-provider"
                }
                """.formatted(vehicleName);

        mockMvc.perform(post("/webhooks/generic")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(videoPayload))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.vehicle.vehicle_name").value(vehicleName));
    }

    @Test
    void testHealthEndpoints() throws Exception {
        // Test vehicle health
        mockMvc.perform(get("/vehicles/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Vehicle service is healthy"));

        // Test webhook health
        mockMvc.perform(get("/webhooks/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("healthy"))
                .andExpect(jsonPath("$.service").value("webhook"));
    }
}
