package com.vlstransmeta.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vlstransmeta.dto.VehicleResponse;
import com.vlstransmeta.exception.VehicleNotFoundException;
import com.vlstransmeta.service.VehicleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(VehicleController.class)
class VehicleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private VehicleService vehicleService;

    @Autowired
    private ObjectMapper objectMapper;

    private VehicleResponse testVehicleResponse;

    @BeforeEach
    void setUp() {
        testVehicleResponse = new VehicleResponse();
        testVehicleResponse.setVehicleName("TEST_VEHICLE_001");
        testVehicleResponse.setLastUpdated(LocalDateTime.now());
        testVehicleResponse.setStatus("ACTIVE");
    }

    @Test
    void testCreateOrGetVehicle_NewVehicle() throws Exception {
        // Given
        when(vehicleService.vehicleExists("TEST_VEHICLE_001")).thenReturn(false);
        when(vehicleService.getVehicle("TEST_VEHICLE_001")).thenReturn(testVehicleResponse);

        // When & Then
        mockMvc.perform(post("/vehicles/TEST_VEHICLE_001"))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.vehicle_name").value("TEST_VEHICLE_001"))
                .andExpect(jsonPath("$.status").value("ACTIVE"));
    }

    @Test
    void testCreateOrGetVehicle_ExistingVehicle() throws Exception {
        // Given
        when(vehicleService.vehicleExists("TEST_VEHICLE_001")).thenReturn(true);
        when(vehicleService.getVehicle("TEST_VEHICLE_001")).thenReturn(testVehicleResponse);

        // When & Then
        mockMvc.perform(post("/vehicles/TEST_VEHICLE_001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpected(jsonPath("$.vehicle_name").value("TEST_VEHICLE_001"));
    }

    @Test
    void testGetVehicle_Success() throws Exception {
        // Given
        when(vehicleService.getVehicle("TEST_VEHICLE_001")).thenReturn(testVehicleResponse);

        // When & Then
        mockMvc.perform(get("/vehicles/TEST_VEHICLE_001"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.vehicle_name").value("TEST_VEHICLE_001"));
    }

    @Test
    void testGetVehicle_NotFound() throws Exception {
        // Given
        when(vehicleService.getVehicle("NONEXISTENT")).thenThrow(new VehicleNotFoundException("NONEXISTENT"));

        // When & Then
        mockMvc.perform(get("/vehicles/NONEXISTENT"))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value("VEHICLE_NOT_FOUND"));
    }

    @Test
    void testVehicleExists_True() throws Exception {
        // Given
        when(vehicleService.vehicleExists("TEST_VEHICLE_001")).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/vehicles/TEST_VEHICLE_001/exists"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testVehicleExists_False() throws Exception {
        // Given
        when(vehicleService.vehicleExists("NONEXISTENT")).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/vehicles/NONEXISTENT/exists"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("false"));
    }

    @Test
    void testDeleteVehicle_Success() throws Exception {
        // When & Then
        mockMvc.perform(delete("/vehicles/TEST_VEHICLE_001"))
                .andExpect(status().isNoContent());
    }

    @Test
    void testDeleteVehicle_NotFound() throws Exception {
        // Given
        when(vehicleService.deleteVehicle(any())).thenThrow(new VehicleNotFoundException("NONEXISTENT"));

        // When & Then
        mockMvc.perform(delete("/vehicles/NONEXISTENT"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testHealth() throws Exception {
        // When & Then
        mockMvc.perform(get("/vehicles/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Vehicle service is healthy"));
    }
}
