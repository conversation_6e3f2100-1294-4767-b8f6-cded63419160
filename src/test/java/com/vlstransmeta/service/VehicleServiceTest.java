package com.vlstransmeta.service;

import com.vlstransmeta.dto.LocationUpdateRequest;
import com.vlstransmeta.dto.VideoUrlUpdateRequest;
import com.vlstransmeta.dto.VehicleResponse;
import com.vlstransmeta.exception.VehicleNotFoundException;
import com.vlstransmeta.model.VehicleData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleServiceTest {

    @Mock
    private S3Service s3Service;

    @Mock
    private LocationService locationService;

    @Mock
    private VideoService videoService;

    @InjectMocks
    private VehicleService vehicleService;

    private VehicleData testVehicle;
    private LocationUpdateRequest locationRequest;
    private VideoUrlUpdateRequest videoRequest;

    @BeforeEach
    void setUp() {
        testVehicle = new VehicleData("TEST_VEHICLE_001");
        
        locationRequest = new LocationUpdateRequest();
        locationRequest.setVehicleName("TEST_VEHICLE_001");
        locationRequest.setLatitude(40.7128);
        locationRequest.setLongitude(-74.0060);
        locationRequest.setTimestamp(LocalDateTime.now());
        
        videoRequest = new VideoUrlUpdateRequest();
        videoRequest.setVehicleName("TEST_VEHICLE_001");
        videoRequest.setVideoUrl("http://example.com/video/stream");
        videoRequest.setExpiryHours(24);
    }

    @Test
    void testCreateOrGetVehicle_NewVehicle() {
        // Given
        when(s3Service.loadVehicleData("TEST_VEHICLE_001")).thenReturn(Optional.empty());
        
        // When
        VehicleData result = vehicleService.createOrGetVehicle("TEST_VEHICLE_001");
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_VEHICLE_001", result.getVehicleName());
        verify(s3Service).saveVehicleData(any(VehicleData.class));
    }

    @Test
    void testCreateOrGetVehicle_ExistingVehicle() {
        // Given
        when(s3Service.loadVehicleData("TEST_VEHICLE_001")).thenReturn(Optional.of(testVehicle));
        
        // When
        VehicleData result = vehicleService.createOrGetVehicle("TEST_VEHICLE_001");
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_VEHICLE_001", result.getVehicleName());
        verify(s3Service, never()).saveVehicleData(any(VehicleData.class));
    }

    @Test
    void testCreateOrGetVehicle_InvalidName() {
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> vehicleService.createOrGetVehicle(null));
        assertThrows(IllegalArgumentException.class, () -> vehicleService.createOrGetVehicle(""));
        assertThrows(IllegalArgumentException.class, () -> vehicleService.createOrGetVehicle("   "));
    }

    @Test
    void testGetVehicle_Success() {
        // Given
        when(s3Service.loadVehicleData("TEST_VEHICLE_001")).thenReturn(Optional.of(testVehicle));
        
        // When
        VehicleResponse result = vehicleService.getVehicle("TEST_VEHICLE_001");
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_VEHICLE_001", result.getVehicleName());
    }

    @Test
    void testGetVehicle_NotFound() {
        // Given
        when(s3Service.loadVehicleData("NONEXISTENT")).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(VehicleNotFoundException.class, () -> vehicleService.getVehicle("NONEXISTENT"));
    }

    @Test
    void testUpdateLocation_Success() {
        // Given
        when(s3Service.loadVehicleData("TEST_VEHICLE_001")).thenReturn(Optional.of(testVehicle));
        when(locationService.isDuplicateLocation(any(), any())).thenReturn(false);
        
        // When
        VehicleResponse result = vehicleService.updateLocation(locationRequest);
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_VEHICLE_001", result.getVehicleName());
        verify(s3Service).saveVehicleData(any(VehicleData.class));
    }

    @Test
    void testUpdateLocation_DuplicateLocation() {
        // Given
        when(s3Service.loadVehicleData("TEST_VEHICLE_001")).thenReturn(Optional.of(testVehicle));
        when(locationService.isDuplicateLocation(any(), any())).thenReturn(true);
        
        // When
        VehicleResponse result = vehicleService.updateLocation(locationRequest);
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_VEHICLE_001", result.getVehicleName());
        // Should not save if duplicate
        verify(s3Service, never()).saveVehicleData(any(VehicleData.class));
    }

    @Test
    void testUpdateVideoUrl_Success() {
        // Given
        when(s3Service.loadVehicleData("TEST_VEHICLE_001")).thenReturn(Optional.of(testVehicle));
        
        // When
        VehicleResponse result = vehicleService.updateVideoUrl(videoRequest);
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_VEHICLE_001", result.getVehicleName());
        verify(s3Service).saveVehicleData(any(VehicleData.class));
    }

    @Test
    void testVehicleExists_True() {
        // Given
        when(s3Service.vehicleExists("TEST_VEHICLE_001")).thenReturn(true);
        
        // When
        boolean result = vehicleService.vehicleExists("TEST_VEHICLE_001");
        
        // Then
        assertTrue(result);
    }

    @Test
    void testVehicleExists_False() {
        // Given
        when(s3Service.vehicleExists("NONEXISTENT")).thenReturn(false);
        
        // When
        boolean result = vehicleService.vehicleExists("NONEXISTENT");
        
        // Then
        assertFalse(result);
    }

    @Test
    void testDeleteVehicle_Success() {
        // Given
        when(s3Service.vehicleExists("TEST_VEHICLE_001")).thenReturn(true);
        
        // When
        vehicleService.deleteVehicle("TEST_VEHICLE_001");
        
        // Then
        verify(s3Service).deleteVehicleData("TEST_VEHICLE_001");
    }

    @Test
    void testDeleteVehicle_NotFound() {
        // Given
        when(s3Service.vehicleExists("NONEXISTENT")).thenReturn(false);
        
        // When & Then
        assertThrows(VehicleNotFoundException.class, () -> vehicleService.deleteVehicle("NONEXISTENT"));
    }
}
